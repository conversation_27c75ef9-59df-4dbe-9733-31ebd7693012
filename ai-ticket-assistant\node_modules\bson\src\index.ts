import * as BSON from './bson';

// Export all named properties from BSON to support
// import { ObjectId, serialize } from 'bson';
// const { ObjectId, serialize } = require('bson');
export * from './bson';

// Export BSON as a namespace to support:
// import { BSON } from 'bson';
// const { BSON } = require('bson');
export { BSON };

// BSON does **NOT** have a default export

// The following will crash in es module environments
// import BSON from 'bson';

// The following will work as expected, BSON as a namespace of all the APIs (BSON.ObjectId, BSON.serialize)
// const BSON = require('bson');
