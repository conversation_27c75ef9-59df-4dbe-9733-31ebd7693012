import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Checkauth from "./compontents/check-auth.jsx";
import Tickets from "./pages/tickets-new.jsx";
import TicketDetailsPage from "./pages/ticket.jsx";
import Login from "./pages/login.jsx";
import Signup from "./pages/signup.jsx";
import Admin from "./pages/admin.jsx";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <BrowserRouter>
      <Routes>
        <Route
          path="/"
          element={
            <Checkauth protectedRoute={true}>
              <Tickets />
            </Checkauth>
          }
        />
         <Route
          path="/tickets/:id"
          element={
            <Checkauth protectedRoute={true}>
              <TicketDetailsPage/>
            </Checkauth>
          }
        />
        <Route
          path="/login"
          element={
            <Checkauth protectedRoute={false}>
              <Login/>
            </Checkauth>
          }
        />
        <Route
          path="/signup"
          element={
            <Checkauth protectedRoute={false}>
              <Signup/>
            </Checkauth>
          }
        />
        <Route
          path="/admin"
          element={
            <Checkauth protectedRoute={true}>
              <Admin/>
            </Checkauth>
          }
        />
      </Routes>
    </BrowserRouter>
  </StrictMode>
);
