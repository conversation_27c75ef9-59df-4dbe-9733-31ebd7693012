{"name": "@types/bunyan", "version": "1.8.11", "description": "TypeScript definitions for bunyan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bunyan", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/amikhalev"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bunyan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "75f683e59cfc06ef80b5a93228bbc7b6bc22f83c76630a237ac8e149f3a75600", "typeScriptVersion": "4.5"}