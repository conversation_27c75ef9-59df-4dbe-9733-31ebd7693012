{"name": "text-encoding", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <filip.dupan<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "Author: <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "version": "0.7.0", "description": "Polyfill for the Encoding Living Standard's API.", "main": "index.js", "files": ["index.js", "lib/encoding.js", "lib/encoding-indexes.js"], "repository": {"type": "git", "url": "https://github.com/inexorabletash/text-encoding.git"}, "keywords": ["encoding", "decoding", "living standard"], "bugs": {"url": "https://github.com/inexorabletash/text-encoding/issues"}, "homepage": "https://github.com/inexorabletash/text-encoding", "license": "(Unlicense OR Apache-2.0)"}