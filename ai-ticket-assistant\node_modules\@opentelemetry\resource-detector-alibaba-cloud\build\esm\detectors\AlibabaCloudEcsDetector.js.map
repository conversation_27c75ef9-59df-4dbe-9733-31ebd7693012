{"version": 3, "file": "AlibabaCloudEcsDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/AlibabaCloudEcsDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAGL,QAAQ,GAGT,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,qCAAqC,EACrC,iCAAiC,EACjC,4BAA4B,EAC5B,mCAAmC,EACnC,0BAA0B,EAC1B,0BAA0B,EAC1B,wBAAwB,EACxB,mBAAmB,EACnB,qBAAqB,EACrB,qBAAqB,GACtB,MAAM,qCAAqC,CAAC;AAE7C,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B;;;;GAIG;AACH;IAAA;QACE;;;WAGG;QACM,gCAA2B,GAAG,iBAAiB,CAAC;QAChD,kDAA6C,GACpD,4CAA4C,CAAC;QACtC,8CAAyC,GAChD,4BAA4B,CAAC;QACtB,0BAAqB,GAAG,IAAI,CAAC;IAwGxC,CAAC;IAtGC;;;;;;OAMG;IACH,wCAAM,GAAN,UAAO,OAAiC;QAAxC,iBAKC;QAJC,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;YACjE,OAAA,KAAI,CAAC,cAAc,EAAE;QAArB,CAAqB,CACtB,CAAC;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,qFAAqF;IAC/E,gDAAc,GAApB,UACE,OAAiC;;;;;;4BAQ7B,qBAAM,IAAI,CAAC,cAAc,EAAE,EAAA;;wBANzB,KAMF,SAA2B,EALT,SAAS,yBAAA,EACd,UAAU,oBAAA,EACR,YAAY,sBAAA,EAChB,MAAM,kBAAA,EACR,gBAAgB,gBAAA;wBAEZ,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAlC,QAAQ,GAAG,SAAuB;wBAExC;gCACE,GAAC,0BAA0B,IAAG,iCAAiC;gCAC/D,GAAC,0BAA0B,IAAG,qCAAqC;gCACnE,GAAC,4BAA4B,IAAG,SAAS;gCACzC,GAAC,wBAAwB,IAAG,MAAM;gCAClC,GAAC,mCAAmC,IAAG,gBAAgB;gCACvD,GAAC,mBAAmB,IAAG,UAAU;gCACjC,GAAC,qBAAqB,IAAG,YAAY;gCACrC,GAAC,qBAAqB,IAAG,QAAQ;qCACjC;;;;KACH;IAED;;;;;OAKG;IACW,gDAAc,GAA5B;;;;;;wBACQ,OAAO,GAAG;4BACd,IAAI,EAAE,IAAI,CAAC,2BAA2B;4BACtC,IAAI,EAAE,IAAI,CAAC,6CAA6C;4BACxD,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE,IAAI,CAAC,qBAAqB;yBACpC,CAAC;wBACe,qBAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;;wBAA3C,QAAQ,GAAG,SAAgC;wBACjD,sBAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAC;;;;KAC7B;IAEa,4CAAU,GAAxB;;;;;;wBACQ,OAAO,GAAG;4BACd,IAAI,EAAE,IAAI,CAAC,2BAA2B;4BACtC,IAAI,EAAE,IAAI,CAAC,yCAAyC;4BACpD,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE,IAAI,CAAC,qBAAqB;yBACpC,CAAC;wBACK,qBAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;4BAAvC,sBAAO,SAAgC,EAAC;;;;KACzC;IAEa,8CAAY,GAA1B,UAA2B,OAA4B;;;;gBACrD,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;wBACjC,IAAM,SAAS,GAAG,UAAU,CAAC;4BAC3B,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;wBAChE,CAAC,EAAE,KAAI,CAAC,qBAAqB,CAAC,CAAC;wBAE/B,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAA,GAAG;4BACnC,YAAY,CAAC,SAAS,CAAC,CAAC;4BAChB,IAAA,UAAU,GAAK,GAAG,WAAR,CAAS;4BAC3B,IACE,OAAO,UAAU,KAAK,QAAQ;gCAC9B,CAAC,CAAC,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,CAAC,EACxC;gCACA,GAAG,CAAC,OAAO,EAAE,CAAC;gCACd,OAAO,MAAM,CACX,IAAI,KAAK,CAAC,uCAAqC,UAAY,CAAC,CAC7D,CAAC;6BACH;4BAED,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;4BACxB,IAAI,OAAO,GAAG,EAAE,CAAC;4BACjB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,UAAA,KAAK,IAAI,OAAA,CAAC,OAAO,IAAI,KAAK,CAAC,EAAlB,CAAkB,CAAC,CAAC;4BAC5C,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,GAAG;gCACjB,MAAM,CAAC,GAAG,CAAC,CAAC;4BACd,CAAC,CAAC,CAAC;4BACH,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE;gCACZ,OAAO,CAAC,OAAO,CAAC,CAAC;4BACnB,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,GAAG;4BACjB,YAAY,CAAC,SAAS,CAAC,CAAC;4BACxB,MAAM,CAAC,GAAG,CAAC,CAAC;wBACd,CAAC,CAAC,CAAC;wBACH,GAAG,CAAC,GAAG,EAAE,CAAC;oBACZ,CAAC,CAAC,EAAC;;;KACJ;IACH,8BAAC;AAAD,CAAC,AAlHD,IAkHC;AAED,MAAM,CAAC,IAAM,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,\n  CLOUDPROVIDERVALUES_ALIBABA_CLOUD,\n  SEMRESATTRS_CLOUD_ACCOUNT_ID,\n  SEMRESATTRS_CLOUD_AVAILABILITY_ZONE,\n  SEMRESATTRS_CLOUD_PLATFORM,\n  SEMRESATTRS_CLOUD_PROVIDER,\n  SEMRESATTRS_CLOUD_REGION,\n  SEMRESATTRS_HOST_ID,\n  SEMRESATTRS_HOST_NAME,\n  SEMRESATTRS_HOST_TYPE,\n} from '@opentelemetry/semantic-conventions';\n\nimport * as http from 'http';\n\n/**\n * The AlibabaCloudEcsDetector can be used to detect if a process is running in\n * AlibabaCloud ECS and return a {@link Resource} populated with metadata about\n * the ECS instance. Returns an empty Resource if detection fails.\n */\nclass AlibabaCloudEcsDetector implements DetectorSync {\n  /**\n   * See https://www.alibabacloud.com/help/doc-detail/67254.htm for\n   * documentation about the AlibabaCloud instance identity document.\n   */\n  readonly ALIBABA_CLOUD_IDMS_ENDPOINT = '***************';\n  readonly ALIBABA_CLOUD_INSTANCE_IDENTITY_DOCUMENT_PATH =\n    '/latest/dynamic/instance-identity/document';\n  readonly ALIBABA_CLOUD_INSTANCE_HOST_DOCUMENT_PATH =\n    '/latest/meta-data/hostname';\n  readonly MILLISECONDS_TIME_OUT = 1000;\n\n  /**\n   * Attempts to connect and obtain an AlibabaCloud instance Identity document.\n   * If the connection is successful it returns a promise containing a\n   * {@link Resource} populated with instance metadata.\n   *\n   * @param config (unused) The resource detection config\n   */\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  /** Gets identity and host info and returns them as attribs. Empty object if fails */\n  async _getAttributes(\n    _config?: ResourceDetectionConfig\n  ): Promise<ResourceAttributes> {\n    const {\n      'owner-account-id': accountId,\n      'instance-id': instanceId,\n      'instance-type': instanceType,\n      'region-id': region,\n      'zone-id': availabilityZone,\n    } = await this._fetchIdentity();\n    const hostname = await this._fetchHost();\n\n    return {\n      [SEMRESATTRS_CLOUD_PROVIDER]: CLOUDPROVIDERVALUES_ALIBABA_CLOUD,\n      [SEMRESATTRS_CLOUD_PLATFORM]: CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,\n      [SEMRESATTRS_CLOUD_ACCOUNT_ID]: accountId,\n      [SEMRESATTRS_CLOUD_REGION]: region,\n      [SEMRESATTRS_CLOUD_AVAILABILITY_ZONE]: availabilityZone,\n      [SEMRESATTRS_HOST_ID]: instanceId,\n      [SEMRESATTRS_HOST_TYPE]: instanceType,\n      [SEMRESATTRS_HOST_NAME]: hostname,\n    };\n  }\n\n  /**\n   * Fetch AlibabaCloud instance document url with http requests. If the\n   * application is running on an ECS instance, we should be able to get back a\n   * valid JSON document. Parses that document and stores the identity\n   * properties in a local map.\n   */\n  private async _fetchIdentity(): Promise<any> {\n    const options = {\n      host: this.ALIBABA_CLOUD_IDMS_ENDPOINT,\n      path: this.ALIBABA_CLOUD_INSTANCE_IDENTITY_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECONDS_TIME_OUT,\n    };\n    const identity = await this._fetchString(options);\n    return JSON.parse(identity);\n  }\n\n  private async _fetchHost(): Promise<string> {\n    const options = {\n      host: this.ALIBABA_CLOUD_IDMS_ENDPOINT,\n      path: this.ALIBABA_CLOUD_INSTANCE_HOST_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECONDS_TIME_OUT,\n    };\n    return await this._fetchString(options);\n  }\n\n  private async _fetchString(options: http.RequestOptions): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        req.destroy(new Error('ECS metadata api request timed out.'));\n      }, this.MILLISECONDS_TIME_OUT);\n\n      const req = http.request(options, res => {\n        clearTimeout(timeoutId);\n        const { statusCode } = res;\n        if (\n          typeof statusCode !== 'number' ||\n          !(statusCode >= 200 && statusCode < 300)\n        ) {\n          res.destroy();\n          return reject(\n            new Error(`Failed to load page, status code: ${statusCode}`)\n          );\n        }\n\n        res.setEncoding('utf8');\n        let rawData = '';\n        res.on('data', chunk => (rawData += chunk));\n        res.on('error', err => {\n          reject(err);\n        });\n        res.on('end', () => {\n          resolve(rawData);\n        });\n      });\n      req.on('error', err => {\n        clearTimeout(timeoutId);\n        reject(err);\n      });\n      req.end();\n    });\n  }\n}\n\nexport const alibabaCloudEcsDetector = new AlibabaCloudEcsDetector();\n"]}