{"name": "@types/tedious", "version": "4.0.14", "description": "TypeScript definitions for tedious", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tedious", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rogierschouten"}, {"name": "<PERSON>", "githubUsername": "cj<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cjthompson"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "guiampm", "url": "https://github.com/guiampm"}, {"name": "<PERSON>", "githubUsername": "<PERSON>sha<PERSON><PERSON>", "url": "https://github.com/csharpsi"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/tedious"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "3571aab8cc66875f1aea89d62d1e5035154a804c591415959d8652a320de40b9", "typeScriptVersion": "4.5"}