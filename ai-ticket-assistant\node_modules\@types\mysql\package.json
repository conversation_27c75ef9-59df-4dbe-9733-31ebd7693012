{"name": "@types/mysql", "version": "2.15.26", "description": "TypeScript definitions for mysql", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mysql", "license": "MIT", "contributors": [{"name": " <PERSON>", "githubUsername": "wjo<PERSON><PERSON>", "url": "https://github.com/wjohnsto"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "kpping", "url": "https://github.com/kpping"}, {"name": "<PERSON>", "githubUsername": "jdmunro", "url": "https://github.com/jdmunro"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/sedenardi"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mysql"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "4a9058442e03f9daed2e7cfebe7369329aee6a3925d645e87bc2691e5520f36c", "typeScriptVersion": "4.7"}